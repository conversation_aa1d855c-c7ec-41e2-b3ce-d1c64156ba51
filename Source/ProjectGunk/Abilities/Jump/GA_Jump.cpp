// Copyright Epic Games, Inc. All Rights Reserved.

#include "GA_Jump.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Components/CapsuleComponent.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "GameplayTagsManager.h"
#include "Net/UnrealNetwork.h"

UGA_Jump::UGA_Jump()
{
	// Set default values
	AbilityInputID = 1; // Assuming jump is input ID 1
	InstancingPolicy = EGameplayAbilityInstancingPolicy::InstancedPerActor;
	NetExecutionPolicy = EGameplayAbilityNetExecutionPolicy::LocalPredicted;
	
	// Set default jump velocity
	JumpVelocity = 500.0f;
	bUseCharacterMovementJumpVelocity = true;

	// Set default gameplay tags
	FGameplayTagsManager& TagsManager = UGameplayTagsManager::Get();
	
	// Ability tags - what this ability is
	AbilityTags.AddTag(TagsManager.RequestGameplayTag(FName("Ability.Jump")));
	
	// Activation required tags - character must be grounded
	ActivationRequiredTags.AddTag(TagsManager.RequestGameplayTag(FName("State.Grounded")));
	
	// Activation blocked tags - can't jump while stunned, etc.
	ActivationBlockedTags.AddTag(TagsManager.RequestGameplayTag(FName("State.Stunned")));
	ActivationBlockedTags.AddTag(TagsManager.RequestGameplayTag(FName("State.Jumping")));
	
	// Cancel abilities tags - what abilities this cancels
	CancelAbilitiesWithTag.AddTag(TagsManager.RequestGameplayTag(FName("Ability.Jump"))); // Cancel other jump attempts
	
	// Block abilities tags - what abilities this blocks
	BlockAbilitiesWithTag.AddTag(TagsManager.RequestGameplayTag(FName("Ability.Jump"))); // Block other jumps while jumping
	
	// Set up tags for internal use
	GroundedTag = TagsManager.RequestGameplayTag(FName("State.Grounded"));
	JumpingTag = TagsManager.RequestGameplayTag(FName("State.Jumping"));
}

bool UGA_Jump::CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags, const FGameplayTagContainer* TargetTags, OUT FGameplayTagContainer* OptionalRelevantTags) const
{
	// Call parent implementation first
	if (!Super::CanActivateAbility(Handle, ActorInfo, SourceTags, TargetTags, OptionalRelevantTags))
	{
		return false;
	}

	// Check if we have a valid character
	const ACharacter* Character = CastChecked<ACharacter>(ActorInfo->AvatarActor.Get(), ECastCheckedType::NullAllowed);
	if (!Character)
	{
		return false;
	}

	// Check if character can jump (has movement component and is not already jumping)
	const UCharacterMovementComponent* CharacterMovement = Character->GetCharacterMovement();
	if (!CharacterMovement)
	{
		return false;
	}

	// Additional check: make sure we're on the ground
	return CharacterMovement->IsMovingOnGround() && !CharacterMovement->IsFalling();
}

void UGA_Jump::ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData)
{
	// Check if we can still activate (network prediction safety)
	if (!HasAuthorityOrPredictionKey(ActorInfo, &ActivationInfo))
	{
		return;
	}

	if (!CommitAbility(Handle, ActorInfo, ActivationInfo))
	{
		EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
		return;
	}

	// Apply costs and cooldowns
	ApplyEffects();

	// Apply the jumping state tag
	if (UAbilitySystemComponent* ASC = ActorInfo->AbilitySystemComponent.Get())
	{
		FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
		EffectContext.AddSourceObject(this);

		// Create a temporary gameplay effect to apply the jumping tag
		UGameplayEffect* JumpingEffect = NewObject<UGameplayEffect>(GetTransientPackage(), FName(TEXT("JumpingEffect")));
		JumpingEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
		JumpingEffect->DurationMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(0.5f)); // Brief duration
		
		// Add the jumping tag
		FInheritedTagContainer TagContainer;
		TagContainer.Added.AddTag(JumpingTag);
		JumpingEffect->InheritableOwnedTagsContainer = TagContainer;

		JumpingEffectHandle = ASC->ApplyGameplayEffectToSelf(JumpingEffect, 1.0f, EffectContext);
	}

	// Perform the actual jump
	PerformJump();

	// Bind to the landed delegate to clean up when we land
	if (ACharacter* Character = CastChecked<ACharacter>(ActorInfo->AvatarActor.Get()))
	{
		Character->LandedDelegate.AddDynamic(this, &UGA_Jump::OnLanded);
	}

	// Call the parent implementation
	Super::ActivateAbility(Handle, ActorInfo, ActivationInfo, TriggerEventData);
}

void UGA_Jump::EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled)
{
	// Remove the jumping effect if it's still active
	if (JumpingEffectHandle.IsValid())
	{
		if (UAbilitySystemComponent* ASC = ActorInfo->AbilitySystemComponent.Get())
		{
			ASC->RemoveActiveGameplayEffect(JumpingEffectHandle);
		}
		JumpingEffectHandle = FActiveGameplayEffectHandle();
	}

	// Unbind from landed delegate
	if (ACharacter* Character = CastChecked<ACharacter>(ActorInfo->AvatarActor.Get(), ECastCheckedType::NullAllowed))
	{
		Character->LandedDelegate.RemoveAll(this);
	}

	Super::EndAbility(Handle, ActorInfo, ActivationInfo, bReplicateEndAbility, bWasCancelled);
}

void UGA_Jump::PerformJump()
{
	if (ACharacter* Character = GetAvatarActorFromActorInfo<ACharacter>())
	{
		if (UCharacterMovementComponent* CharacterMovement = Character->GetCharacterMovement())
		{
			// Use character movement's jump velocity if specified, otherwise use our own
			if (bUseCharacterMovementJumpVelocity)
			{
				Character->Jump();
			}
			else
			{
				// Set custom jump velocity
				const float OldJumpVelocity = CharacterMovement->JumpZVelocity;
				CharacterMovement->JumpZVelocity = JumpVelocity;
				Character->Jump();
				CharacterMovement->JumpZVelocity = OldJumpVelocity; // Restore original value
			}
		}
	}
}

void UGA_Jump::OnLanded(const FHitResult& Hit)
{
	// End the ability when we land
	if (IsActive())
	{
		EndAbility(CurrentSpecHandle, CurrentActorInfo, CurrentActivationInfo, true, false);
	}
}

void UGA_Jump::ApplyEffects()
{
	if (UAbilitySystemComponent* ASC = GetAbilitySystemComponentFromActorInfo())
	{
		FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
		EffectContext.AddSourceObject(this);

		// Apply cost effect (e.g., stamina cost)
		if (CostGameplayEffectClass)
		{
			FGameplayEffectSpecHandle CostEffectSpec = ASC->MakeOutgoingSpec(CostGameplayEffectClass, 1.0f, EffectContext);
			if (CostEffectSpec.IsValid())
			{
				ASC->ApplyGameplayEffectSpecToSelf(*CostEffectSpec.Data.Get());
			}
		}

		// Apply cooldown effect
		if (CooldownGameplayEffectClass)
		{
			FGameplayEffectSpecHandle CooldownEffectSpec = ASC->MakeOutgoingSpec(CooldownGameplayEffectClass, 1.0f, EffectContext);
			if (CooldownEffectSpec.IsValid())
			{
				ASC->ApplyGameplayEffectSpecToSelf(*CooldownEffectSpec.Data.Get());
			}
		}
	}
}