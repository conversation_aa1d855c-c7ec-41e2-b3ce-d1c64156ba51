// Copyright Epic Games, Inc. All Rights Reserved.

// ReSharper disable CppRedundantAccessSpecifier
#pragma once

#include "CoreMinimal.h"
#include "Abilities/GSCGameplayAbility.h"
#include "GameplayTagContainer.h"
#include "GA_Jump.generated.h"

class UGameplayEffect;

/**
 * Jump ability for ProjectGunk characters using GASCompanion
 * Handles jump logic with cooldown and cost using Gameplay Effects
 */
UCLASS(BlueprintType, Blueprintable, meta = (DisplayName = "Jump Ability"))
class PROJECTGUNK_API UGA_Jump : public UGSCGameplayAbility
{
	GENERATED_BODY()

public:
	UGA_Jump();

protected:
	// Core ability implementation
	virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;
	virtual bool CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags = nullptr, const FGameplayTagContainer* TargetTags = nullptr, OUT FGameplayTagContainer* OptionalRelevantTags = nullptr) const override;
	virtual void EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled) override;

protected:
	/** Jump velocity to apply to the character */
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Jump", meta = (ClampMin = "0.0"))
	float JumpVelocity;

	/** Whether to use the character movement component's jump velocity instead of our own */
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Jump")
	bool bUseCharacterMovementJumpVelocity;

	/** Cooldown Gameplay Effect class to apply after jumping */
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Jump")
	TSubclassOf<UGameplayEffect> CooldownGameplayEffectClass;

	/** Cost Gameplay Effect class to apply when jumping (e.g., stamina cost) */
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Jump")
	TSubclassOf<UGameplayEffect> CostGameplayEffectClass;

	/** Gameplay tag to check if character is grounded */
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Jump")
	FGameplayTag GroundedTag;

	/** Gameplay tag applied when jumping */
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Jump")
	FGameplayTag JumpingTag;

private:
	/** Handle for the jumping gameplay effect (to remove it when landing) */
	FActiveGameplayEffectHandle JumpingEffectHandle;

	/** Performs the actual jump logic */
	void PerformJump();

	/** Called when the character lands */
	UFUNCTION()
	void OnLanded(const FHitResult& Hit);

	/** Apply gameplay effects (cooldown and cost) */
	void ApplyEffects();
};